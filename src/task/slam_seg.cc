#include "slam_seg.h"
#include "utils/utils.h"
#include "shunzao_ai_task.h"
#include "shunzao_ai_lib.h"
#include <rapidjson/document.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>

extern int PRINT_LOG;

SlamSeg::SlamSeg(const char* model_path) {
    if (!setup(model_path)) {
        std::cerr << "[SlamSeg] Constructor failed to setup with model: " << model_path << std::endl;
    }

    // 计算最大数据大小：分割掩码大小 + 额外信息
    max_data_size_ = output_h * output_w + 1024;  // 预留额外空间
    res_addr_ = (char*)malloc(max_data_size_);
}

SlamSeg::~SlamSeg() {
    if(res_addr_) {
        free(res_addr_);
        res_addr_ = nullptr;
    }
}

bool SlamSeg::setup(const std::string& model_path) {
    std::cout << "[SlamSeg] Setting up with model: " << model_path << std::endl;
    // 使用 network_id = 0, 默认优先级 = 128
    nn_in_width_ = input_w;
    nn_in_height_ = input_h;
    nn_in_channel_ = input_c;
    return init(model_path.c_str(), 0);
}

int SlamSeg::loadconfig(std::string config_string) {
    std::cout << "[SlamSeg] Loading config string: " << config_string << std::endl;
    rapidjson::Document document;
    document.Parse(config_string.data());

    if (document.HasParseError()) {
        std::cout << "[SlamSeg] Loading config file error" << std::endl;
        return -1;
    }

    // 解析配置参数
    if (document.HasMember("confidence_threshold") && document["confidence_threshold"].IsNumber()) {
        confidence_threshold_ = document["confidence_threshold"].GetFloat();
        std::cout << "[SlamSeg] confidence_threshold: " << confidence_threshold_ << std::endl;
    }

    if (document.HasMember("debug_show_result") && document["debug_show_result"].IsInt()) {
        debug_show_result = document["debug_show_result"].GetInt();
        std::cout << "[SlamSeg] debug_show_result: " << debug_show_result << std::endl;
    }

    return 0;
}

bool SlamSeg::run_inference(cv::Mat& img, InputParam* inp) {
    std::cout << "[SlamSeg] Running inference..." << std::endl;
    int ret = 0;
    file_data = this->preprocess(img, inp->coreid, &file_size);
    ret = load_input_set_output(file_data, file_size);

    if (!run_once()) {
        std::cerr << "[SlamSeg] Network run failed." << std::endl;
        return false;
    }

    get_output_data(output_data);  // 获取网络最终的输出矩阵
    if (!output_data) {
        std::cerr << "[SlamSeg] Failed to get output." << std::endl;
        return false;
    }

    if (this->postprocess(img, output_data) != 0) {
        std::cerr << "[SlamSeg] postprocess failed." << std::endl;
        return false;
    }

    // 释放存储模型output_data的内存
    delete_output_data(output_data);
    return true;
}

// bool SlamSeg::run_inference_with_time(cv::Mat& img, InputParam* inp) {
//     std::cout << "[SlamSeg] Running inference with time..." << std::endl;
//     struct timeval start_time, end_time;

//     gettimeofday(&start_time, nullptr);
//     bool result = run_inference(img, inp);
//     gettimeofday(&end_time, nullptr);

//     long long total_time = (end_time.tv_sec - start_time.tv_sec) * 1000000 +
//                           (end_time.tv_usec - start_time.tv_usec);
//     std::cout << "[SlamSeg] Total inference time: " << total_time << " microseconds" << std::endl;

//     return result;
// }

bool SlamSeg::run_inference_with_time(cv::Mat& img, InputParam* inp) {
    // 记录总开始时间
    auto total_start = std::chrono::high_resolution_clock::now();
    
    std::cout << "[SlamSeg] Running inference..." << std::endl;
    int ret = 0;

    // 1. 预处理阶段计时
    std::cout << "[SlamSeg] Preprocess start" << std::endl;
    auto preprocess_start = std::chrono::high_resolution_clock::now();
    file_data = this->preprocess(img, inp->coreid, &file_size);
    auto preprocess_end = std::chrono::high_resolution_clock::now();
    auto preprocess_time = std::chrono::duration_cast<std::chrono::microseconds>(
        preprocess_end - preprocess_start);
    std::cout << "[SlamSeg] 预处理时间: " << preprocess_time.count() /1000.0 << " ms" << std::endl;

    // 2. 输入加载阶段计时
    std::cout << "[SlamSeg] Load input start" << std::endl;  // 添加开始标志
    auto load_start = std::chrono::high_resolution_clock::now();
    ret = load_input_set_output(file_data, file_size);
    auto load_end = std::chrono::high_resolution_clock::now();
    auto load_time = std::chrono::duration_cast<std::chrono::microseconds>(
        load_end - load_start);
    std::cout << "[SlamSeg] 加载输入时间: " << load_time.count() /1000.0 << " ms" << std::endl;

    // 3. 推理阶段计时
    std::cout << "[SlamSeg] NPU inference start" << std::endl;  // 添加开始标志
    auto inference_start = std::chrono::high_resolution_clock::now();
    if (!run_once()) {
        std::cerr << "[SlamSeg] Network run failed." << std::endl;
        return false;
    }
    auto inference_end = std::chrono::high_resolution_clock::now();
    auto inference_time = std::chrono::duration_cast<std::chrono::microseconds>(
        inference_end - inference_start);
    std::cout << "[SlamSeg] NPU 推理时间: " << inference_time.count() /1000.0 << " ms" << std::endl;

    // 4. 获取输出阶段计时
    std::cout << "[SlamSeg] Get output start" << std::endl;  // 添加开始标志
    auto output_start = std::chrono::high_resolution_clock::now();
    get_output_data(output_data);  // 获取网络最终的输出矩阵
    auto output_end = std::chrono::high_resolution_clock::now();
    auto output_time = std::chrono::duration_cast<std::chrono::microseconds>(
        output_end - output_start);
    std::cout << "[SlamSeg] 获取输出时间: " << output_time.count() /1000.0 << " ms" << std::endl;

    // 5. 后处理阶段计时
    std::cout << "[SlamSeg] Postprocess start" << std::endl;  // 添加开始标志
    auto postprocess_start = std::chrono::high_resolution_clock::now();
    int postprocess_result = this->postprocess(img, output_data);
    auto postprocess_end = std::chrono::high_resolution_clock::now();
    auto postprocess_time = std::chrono::duration_cast<std::chrono::microseconds>(
        postprocess_end - postprocess_start);
    std::cout << "[SlamSeg] 后处理时间: " << postprocess_time.count() /1000.0 << " ms" << std::endl;
    
    if (postprocess_result != 0) {
        std::cerr << "[SlamSeg] postprocess failed." << std::endl;
        return false;
    }

    // 6. 内存清理阶段计时
    std::cout << "[SlamSeg] Cleanup start" << std::endl;  // 添加开始标志
    auto cleanup_start = std::chrono::high_resolution_clock::now();
    delete_output_data(output_data);
    auto cleanup_end = std::chrono::high_resolution_clock::now();
    auto cleanup_time = std::chrono::duration_cast<std::chrono::microseconds>(
        cleanup_end - cleanup_start);
    std::cout << "[SlamSeg] Cleanup: " << cleanup_time.count()/1000.0 << " ms" << std::endl;

    // 7. 总时间统计
    auto total_end = std::chrono::high_resolution_clock::now();
    auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
        total_end - total_start);
    
    // 打印详细时间分析
    std::cout << "\n[SlamSeg Performance Summary]" << std::endl;
    std::cout << "--------------------------------" << std::endl;
    std::cout << "Total time:      " << total_time.count() /1000.0 << " ms" << std::endl;
    std::cout << "Preprocess:      " << preprocess_time.count() << " μs (" 
              << 100.0 * preprocess_time.count() / total_time.count() << "%)" << std::endl;
    std::cout << "Input loading:   " << load_time.count() << " μs (" 
              << 100.0 * load_time.count() / total_time.count() << "%)" << std::endl;
    std::cout << "NPU inference:   " << inference_time.count() << " μs (" 
              << 100.0 * inference_time.count() / total_time.count() << "%)" << std::endl;
    std::cout << "Output retrieval:" << output_time.count() << " μs (" 
              << 100.0 * output_time.count() / total_time.count() << "%)" << std::endl;
    std::cout << "Postprocess:     " << postprocess_time.count() << " μs (" 
              << 100.0 * postprocess_time.count() / total_time.count() << "%)" << std::endl;
    std::cout << "Cleanup:         " << cleanup_time.count() << " μs (" 
              << 100.0 * cleanup_time.count() / total_time.count() << "%)" << std::endl;
    std::cout << "--------------------------------" << std::endl;

    return true;
}


bool SlamSeg::run_inference(cv::Mat& img, InputParam* inp, shunzaoAiTask* outer) {
    std::cout << "[SlamSeg] Running inference with timing..." << std::endl;
    int ret = 0;
    struct timeval run_start{}, run_end{};

    // 预处理计时
    gettimeofday(&run_start, nullptr);
    file_data = this->preprocess(img, inp->coreid, &file_size);
    ret = load_input_set_output(file_data, file_size);
    gettimeofday(&run_end, nullptr);
    long long preprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
                               (run_end.tv_usec - run_start.tv_usec);
    outer->setPreprocessTime(preprocess_time);

    // 推理计时
    gettimeofday(&run_start, nullptr);
    if (!run_once()) {
        std::cerr << "[SlamSeg] Network run failed." << std::endl;
        return false;
    }
    get_output_data(output_data);
    gettimeofday(&run_end, nullptr);
    long long inference_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
                              (run_end.tv_usec - run_start.tv_usec);
    outer->setInferenceTime(inference_time);

    if (!output_data) {
        std::cerr << "[SlamSeg] Failed to get output." << std::endl;
        return false;
    }

    // 后处理计时
    gettimeofday(&run_start, nullptr);
    if (this->postprocess(img, output_data) != 0) {
        std::cerr << "[SlamSeg] postprocess failed." << std::endl;
        return false;
    }
    gettimeofday(&run_end, nullptr);
    long long postprocess_time = (run_end.tv_sec - run_start.tv_sec) * 1000000 +
                                (run_end.tv_usec - run_start.tv_usec);
    outer->setPostprocessTime(postprocess_time);
    outer->setCount();

    // 释放存储模型output_data的内存
    delete_output_data(output_data);
    return true;
}

int SlamSeg::postprocess(cv::Mat& img, float** output_tensor) {
    std::cout << "[SlamSeg] postprocess..." << std::endl;

    seg_results_.clear(); // 清空结果

    // 模型输出: (1, 19, 128, 256)
    // output_tensor[0] 包含所有输出数据
    float* output_data = output_tensor[0];
    // std::cout << "[SlamSeg] Output data: " << output_data << std::endl;
    if (!output_data) {
        std::cerr << "[SlamSeg] Output data is null" << std::endl;
        return -1;
    }

    std::cout << "[SlamSeg] Processing segmentation output..." << std::endl;
    std::cout << "[SlamSeg] Original image size: " << img.cols << "x" << img.rows << std::endl;
    std::cout << "[SlamSeg] Network output size: " << output_w << "x" << output_h << std::endl;
    std::cout << "[SlamSeg] Number of classes: " << num_classes << std::endl;


    // 先将分割输出上采样到原图尺寸，再进行argmax操作
    SegMask final_mask = resize_and_argmax(output_data, img.rows, img.cols, num_classes);

    // 临时测试：也创建一个低分辨率的argmax结果进行对比
    SegMask low_res_mask = simple_argmax(output_data, output_h, output_w, num_classes);

    // 计算整体置信度（可以根据需要调整计算方法）
    // float confidence = 0.8f;  // 简单设置，可以根据实际需求计算

    // 创建分割结果
    SegMaskFlag seg_result;
    seg_result.mask = final_mask;
    // seg_result.confidence = confidence;
    seg_result.flag = 1.0f;  // 标志位，表示有效结果

    seg_results_.push_back(seg_result);

    if (debug_show_result) {
        std::cout << "[SlamSeg] Segmentation result:" << std::endl;
        std::cout << "  - Mask size: " << final_mask.width << "x" << final_mask.height << std::endl;
        std::cout << "  - Number of classes: " << final_mask.num_classes << std::endl;
        // std::cout << "  - Confidence: " << confidence << std::endl;

        // 统计各类别像素数量
        std::vector<int> class_counts(num_classes, 0);
        for (size_t i = 0; i < final_mask.mask_data.size(); i++) {
            if (final_mask.mask_data[i] < num_classes) {
                class_counts[final_mask.mask_data[i]]++;
            }
        }

        std::cout << "  - Class pixel counts:" << std::endl;
        for (int i = 0; i < num_classes; i++) {
            if (class_counts[i] > 0) {
                std::cout << "    Class " << i << ": " << class_counts[i] << " pixels" << std::endl;
            }
        }
    }

    return 0;
}

SegMask SlamSeg::resize_and_argmax(float* output_data, int original_height, int original_width, int num_classes) {
    std::cout << "[SlamSeg] Resizing from " << output_w << "x" << output_h
              << " to " << original_width << "x" << original_height << " then applying argmax..." << std::endl;

    // 调试：检查输出数据的一些值
    std::cout << "[SlamSeg] Debug: First few output values: ";
    for (int i = 0; i < std::min(10, output_h * output_w); i++) {
        std::cout << output_data[i] << " ";
    }
    std::cout << std::endl;

    // 创建目标尺寸
    cv::Size target_size(original_width, original_height);

    // 存储所有通道的上采样结果
    std::vector<cv::Mat> resized_channels(num_classes);

    // 对每个类别通道进行双线性插值上采样
    for (int c = 0; c < num_classes; c++) {
        // 获取当前通道的数据
        float* channel_data = output_data + c * output_h * output_w;
        cv::Mat channel_mat(output_h, output_w, CV_32FC1, channel_data);

        // 调试：检查每个通道的数据范围
        double min_val, max_val;
        cv::minMaxLoc(channel_mat, &min_val, &max_val);
        if (c < 3) { // 只打印前3个通道的信息
            std::cout << "[SlamSeg] Channel " << c << " range before exp: [" << min_val << ", " << max_val << "]" << std::endl;
        }

        // 应用指数函数 (类似Python的pred.exp())
        cv::Mat exp_channel_mat;
        cv::exp(channel_mat, exp_channel_mat);

        if (c < 3) { // 打印exp后的范围
            cv::minMaxLoc(exp_channel_mat, &min_val, &max_val);
            std::cout << "[SlamSeg] Channel " << c << " range after exp: [" << min_val << ", " << max_val << "]" << std::endl;
        }

        // 双线性插值调整大小到原图尺寸
        cv::resize(exp_channel_mat, resized_channels[c], target_size, 0, 0, cv::INTER_LINEAR);
    }

    // 创建最终的分割掩码
    SegMask final_mask(original_width, original_height, num_classes);

    // 统计类别分布
    std::vector<int> class_counts(num_classes, 0);

    // 对每个像素执行argmax操作
    for (int y = 0; y < original_height; y++) {
        for (int x = 0; x < original_width; x++) {
            int best_class = 0;
            float max_prob = -FLT_MAX;

            // 在所有类别中寻找概率最大的类别
            for (int c = 0; c < num_classes; c++) {
                float prob = resized_channels[c].at<float>(y, x);

                if (prob > max_prob) {
                    max_prob = prob;
                    best_class = c;
                }
            }

            // 将最佳类别索引存入掩码
            int idx = y * original_width + x;
            final_mask.mask_data[idx] = static_cast<uint8_t>(best_class);
            class_counts[best_class]++;
        }
    }

    // 调试：打印类别分布
    std::cout << "[SlamSeg] Class distribution after argmax:" << std::endl;
    for (int c = 0; c < num_classes; c++) {
        if (class_counts[c] > 0) {
            std::cout << "  Class " << c << ": " << class_counts[c] << " pixels ("
                      << (100.0 * class_counts[c] / (original_width * original_height)) << "%)" << std::endl;
        }
    }

    return final_mask;
}

SegMask SlamSeg::simple_argmax(float* output_data, int height, int width, int num_classes) {
    std::cout << "[SlamSeg] Simple argmax on " << width << "x" << height << " with " << num_classes << " classes" << std::endl;

    // 创建低分辨率掩码
    SegMask mask(width, height, num_classes);

    // 统计类别分布
    std::vector<int> class_counts(num_classes, 0);

    // 对每个像素执行argmax操作
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int best_class = 0;
            float max_prob = -FLT_MAX;

            // 在所有类别中寻找概率最大的类别
            for (int c = 0; c < num_classes; c++) {
                // NCHW格式：[batch, channel, height, width]
                int idx = c * height * width + y * width + x;
                float logit = output_data[idx];
                float prob = exp(logit);  // 应用指数函数

                if (prob > max_prob) {
                    max_prob = prob;
                    best_class = c;
                }
            }

            // 将最佳类别索引存入掩码
            int mask_idx = y * width + x;
            mask.mask_data[mask_idx] = static_cast<uint8_t>(best_class);
            class_counts[best_class]++;
        }
    }

    // 打印类别分布
    std::cout << "[SlamSeg] Low-res class distribution:" << std::endl;
    for (int c = 0; c < num_classes; c++) {
        if (class_counts[c] > 0) {
            std::cout << "  Class " << c << ": " << class_counts[c] << " pixels ("
                      << (100.0 * class_counts[c] / (width * height)) << "%)" << std::endl;
        }
    }

    return mask;
}

std::vector<SegMaskFlag> SlamSeg::get_prediction(int* size) {
    std::cout << "[SlamSeg] get_prediction..." << std::endl;

    if (size) {
        *size = seg_results_.size();
    }

    std::cout << "[SlamSeg] Returning " << seg_results_.size() << " segmentation results" << std::endl;
    return seg_results_;
}